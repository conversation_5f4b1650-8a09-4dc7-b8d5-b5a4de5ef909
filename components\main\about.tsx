"use client";

import {
  motion,
  useScroll,
  useTransform,
  useInView,
} from "framer-motion";
import Image from "next/image";
import { useRef, useState } from "react";

import { GlowingEffect } from "@/components/ui/glowing-effect";

// Animated counter component
const AnimatedCounter = ({
  value,
  label,
  color,
  delay
}: {
  value: number;
  label: string;
  color: string;
  delay: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.5 });
  const [displayValue, setDisplayValue] = useState(0);

  // Animate the counter when in view
  if (isInView && displayValue !== value) {
    setTimeout(() => {
      const interval = setInterval(() => {
        setDisplayValue(prev => {
          const newValue = prev + 1;
          if (newValue >= value) {
            clearInterval(interval);
            return value;
          }
          return newValue;
        });
      }, 30);

      return () => clearInterval(interval);
    }, delay);
  }

  return (
    <motion.div
      ref={ref}
      initial={{ opacity: 0, y: 20, scale: 0.9 }}
      animate={isInView ? { opacity: 1, y: 0, scale: 1 } : { opacity: 0, y: 20, scale: 0.9 }}
      transition={{ duration: 0.5, delay: delay / 1000 }}
      className="relative bg-gradient-to-br from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.5)] to-[rgba(3,0,20,0.6)] border border-[#7042F88B] rounded-2xl p-6 flex items-center justify-center flex-col min-w-[140px] h-[120px] backdrop-blur-xl overflow-hidden group hover:border-purple-500/50 transition-all duration-500"
      whileHover={{ scale: 1.05, y: -5 }}
    >
      <GlowingEffect
        spread={80}
        glow={true}
        disabled={false}
        proximity={120}
        inactiveZone={0.1}
        blur={3}
        borderWidth={2}
      />

      {/* Animated background glow */}
      <div
        className={`absolute inset-0 opacity-0 group-hover:opacity-30 transition-opacity duration-700 bg-gradient-to-br ${
          color === "purple" ? "from-purple-600/20 to-purple-400/10" : "from-cyan-600/20 to-cyan-400/10"
        } rounded-2xl`}
      />

      {/* Animated number */}
      <motion.span
        className={`text-4xl font-bold relative z-10 ${color === "purple" ? "text-purple-400" : "text-cyan-400"} group-hover:text-white transition-colors duration-300`}
        initial={{ opacity: 0, y: 10 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
        transition={{ duration: 0.3, delay: delay / 1000 }}
        whileHover={{ scale: 1.1 }}
      >
        {displayValue}+
      </motion.span>

      {/* Label */}
      <motion.span
        className="text-gray-300 text-sm font-medium relative z-10 group-hover:text-white transition-colors duration-300"
        initial={{ opacity: 0 }}
        animate={isInView ? { opacity: 1 } : { opacity: 0 }}
        transition={{ duration: 0.3, delay: (delay + 300) / 1000 }}
      >
        {label}
      </motion.span>

      {/* Decorative corner elements */}
      <div className={`absolute top-2 right-2 w-2 h-2 rounded-full ${color === "purple" ? "bg-purple-500" : "bg-cyan-500"} opacity-50 group-hover:opacity-100 transition-opacity duration-300`} />
      <div className={`absolute bottom-2 left-2 w-2 h-2 rounded-full ${color === "purple" ? "bg-purple-500" : "bg-cyan-500"} opacity-50 group-hover:opacity-100 transition-opacity duration-300`} />
    </motion.div>
  );
};

// Modern animated text with word-by-word reveal
const AnimatedText = ({
  text,
  className,
  delay = 0
}: {
  text: string;
  className: string;
  delay?: number;
}) => {
  const ref = useRef(null);
  const isInView = useInView(ref, { once: true, amount: 0.3 });

  const words = text.split(' ');

  return (
    <motion.p
      ref={ref}
      className={className}
      initial={{ opacity: 0, y: 20 }}
      animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
      transition={{ duration: 0.6, delay, ease: "easeOut" }}
    >
      {words.map((word, wordIndex) => (
        <motion.span
          key={`word-${word}-${wordIndex}`}
          className="inline-block mr-1"
          initial={{ opacity: 0, y: 10 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 10 }}
          transition={{
            duration: 0.4,
            delay: delay + (wordIndex * 0.03),
            ease: "easeOut"
          }}
        >
          {word}{' '}
        </motion.span>
      ))}
    </motion.p>
  );
};

export const About = () => {
  const sectionRef = useRef(null);
  const imageRef = useRef(null);
  const contentRef = useRef(null);
  const isInView = useInView(sectionRef, { once: true, amount: 0.1 });
  const imageInView = useInView(imageRef, { once: true });
  const contentInView = useInView(contentRef, { once: true, amount: 0.2 });

  // Parallax effect for the image
  const { scrollYProgress } = useScroll({
    target: sectionRef,
    offset: ["start end", "end start"]
  });

  const y = useTransform(scrollYProgress, [0, 1], [-30, 30]);
  const scale = useTransform(scrollYProgress, [0, 0.5, 1], [0.95, 1.02, 1]);

  return (
    <section
      id="about-us"
      ref={sectionRef}
      className="relative min-h-screen flex flex-col items-center justify-center py-24 px-4 lg:px-8 overflow-hidden"
    >
      {/* Enhanced background elements */}
      <div className="absolute inset-0 bg-gradient-to-b from-transparent via-[rgba(3,0,20,0.3)] to-transparent pointer-events-none" />

      <motion.div
        className="absolute top-32 left-10 w-96 h-96 bg-purple-500/10 rounded-full blur-3xl"
        style={{
          x: useTransform(scrollYProgress, [0, 1], [-50, 50]),
          y: useTransform(scrollYProgress, [0, 1], [-20, 20]),
          opacity: useTransform(scrollYProgress, [0, 0.5, 1], [0.2, 0.4, 0.2])
        }}
      />

      <motion.div
        className="absolute bottom-32 right-10 w-96 h-96 bg-cyan-500/10 rounded-full blur-3xl"
        style={{
          x: useTransform(scrollYProgress, [0, 1], [50, -50]),
          y: useTransform(scrollYProgress, [0, 1], [20, -20]),
          opacity: useTransform(scrollYProgress, [0, 0.5, 1], [0.2, 0.4, 0.2])
        }}
      />

      {/* Floating particles */}
      {Array.from({ length: 6 }).map((_, i) => (
        <motion.div
          key={i}
          className={`absolute w-1 h-1 rounded-full ${i % 2 === 0 ? 'bg-purple-500/30' : 'bg-cyan-500/30'}`}
          style={{
            left: `${20 + i * 15}%`,
            top: `${30 + i * 10}%`,
          }}
          animate={{
            y: [0, -20, 0],
            opacity: [0.3, 0.8, 0.3],
          }}
          transition={{
            duration: 3 + i,
            repeat: Infinity,
            delay: i * 0.5,
          }}
        />
      ))}

      {/* Enhanced section header */}
      <motion.div
        className="text-center mb-16 max-w-4xl mx-auto"
        initial={{ opacity: 0, y: 30 }}
        animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 30 }}
        transition={{ duration: 0.8, ease: "easeOut" }}
      >
        <motion.div
          className="Welcome-box py-3 px-6 border border-[#7042f88b] mb-8 mx-auto"
          initial={{ opacity: 0, scale: 0.9 }}
          animate={isInView ? { opacity: 1, scale: 1 } : { opacity: 0, scale: 0.9 }}
          transition={{ duration: 0.6, delay: 0.2 }}
        >
          <motion.h1
            className="Welcome-text text-sm font-medium text-center"
            initial={{ opacity: 0, filter: "blur(8px)" }}
            animate={isInView ? {
              opacity: 1,
              filter: "blur(0px)",
              transition: { duration: 0.8, delay: 0.3 }
            } : {}}
          >
            Who We Are
          </motion.h1>
        </motion.div>

        <motion.h2
          className="text-5xl lg:text-6xl font-bold text-white mb-6"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.4 }}
        >
          <span>About </span>
          <span className="text-transparent bg-clip-text bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-600">
            Us
          </span>
        </motion.h2>

        <motion.p
          className="text-xl text-gray-300 leading-relaxed max-w-2xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={isInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
          transition={{ duration: 0.6, delay: 0.6 }}
        >
          Transforming businesses through innovative digital solutions and cutting-edge technology
        </motion.p>
      </motion.div>

      {/* Main content container */}
      <div className="w-full max-w-7xl mx-auto">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-16 items-center">
          {/* Left content with glassmorphism container */}
          <motion.div
            ref={contentRef}
            initial={{ opacity: 0, x: -50 }}
            animate={contentInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -50 }}
            transition={{ duration: 0.8, delay: 0.2 }}
            className="relative"
          >
            <div className="relative p-8 lg:p-12 rounded-3xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.5)] to-[rgba(3,0,20,0.6)] border border-[#7042F88B] hover:border-purple-500/50 transition-all duration-500">
              <GlowingEffect
                spread={120}
                glow={true}
                disabled={false}
                proximity={150}
                inactiveZone={0.05}
                blur={4}
                borderWidth={3}
              />

              {/* Mission section */}
              <motion.div
                className="mb-10 relative z-10"
                initial={{ opacity: 0, y: 20 }}
                animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.4 }}
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-purple-500 to-purple-600 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M13 10V3L4 14h7v7l9-11h-7z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-white">Our Mission</h3>
                </div>

                <AnimatedText
                  text="At Oyu Intelligence LLC, we're a Mongolian Start-Up company dedicated to transforming businesses through innovative digital solutions. Our mission is to empower organizations with cutting-edge technology that drives growth, enhances efficiency, and creates exceptional user experiences."
                  className="text-gray-300 text-lg leading-relaxed"
                  delay={0.6}
                />
              </motion.div>

              {/* Approach section */}
              <motion.div
                className="mb-10 relative z-10"
                initial={{ opacity: 0, y: 20 }}
                animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 0.8 }}
              >
                <div className="flex items-center mb-6">
                  <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-cyan-500 to-cyan-600 flex items-center justify-center mr-4">
                    <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M17 20h5v-2a3 3 0 00-5.356-1.857M17 20H7m10 0v-2c0-.656-.126-1.283-.356-1.857M7 20H2v-2a3 3 0 015.356-1.857M7 20v-2c0-.656.126-1.283.356-1.857m0 0a5.002 5.002 0 019.288 0M15 7a3 3 0 11-6 0 3 3 0 016 0zm6 3a2 2 0 11-4 0 2 2 0 014 0zM7 10a2 2 0 11-4 0 2 2 0 014 0z" />
                    </svg>
                  </div>
                  <h3 className="text-3xl font-bold text-white">Our Approach</h3>
                </div>

                <AnimatedText
                  text="We believe in a collaborative approach, working closely with our clients to understand their unique challenges and goals. By combining technical expertise with creative thinking, we deliver tailored solutions that exceed expectations and provide lasting value."
                  className="text-gray-300 text-lg leading-relaxed"
                  delay={1.0}
                />
              </motion.div>

              {/* Stats with enhanced layout */}
              <motion.div
                className="relative z-10"
                initial={{ opacity: 0, y: 20 }}
                animate={contentInView ? { opacity: 1, y: 0 } : { opacity: 0, y: 20 }}
                transition={{ duration: 0.6, delay: 1.2 }}
              >
                <h4 className="text-xl font-semibold text-white mb-6 text-center">Our Achievements</h4>
                <div className="grid grid-cols-2 gap-4">
                  <AnimatedCounter value={100} label="Projects" color="purple" delay={1400} />
                  <AnimatedCounter value={20} label="Clients" color="cyan" delay={1600} />
                  <AnimatedCounter value={3} label="Brand Partners" color="purple" delay={1800} />
                  <AnimatedCounter value={4} label="Services" color="cyan" delay={2000} />
                </div>
              </motion.div>
            </div>
          </motion.div>

          {/* Right side - Enhanced visual showcase */}
          <motion.div
            ref={imageRef}
            initial={{ opacity: 0, scale: 0.8, x: 50 }}
            animate={imageInView ? {
              opacity: 1,
              scale: 1,
              x: 0,
              transition: {
                duration: 0.8,
                delay: 0.4,
                type: "spring",
                stiffness: 100
              }
            } : { opacity: 0, scale: 0.8, x: 50 }}
            className="relative flex justify-center items-center"
            style={{ y, scale }}
          >
            {/* Main image container with enhanced effects */}
            <div className="relative w-[400px] h-[400px] lg:w-[500px] lg:h-[500px]">
              {/* Multiple layered glow effects */}
              <motion.div
                className="absolute inset-0 bg-gradient-to-r from-purple-500/30 to-cyan-500/30 rounded-full blur-3xl"
                animate={{
                  scale: [1, 1.2, 1, 0.9, 1],
                  opacity: [0.3, 0.6, 0.3, 0.2, 0.3],
                  rotate: [0, 180, 360]
                }}
                transition={{ duration: 12, repeat: Infinity }}
              />

              <motion.div
                className="absolute inset-4 bg-gradient-to-l from-cyan-500/20 to-purple-500/20 rounded-full blur-2xl"
                animate={{
                  scale: [0.9, 1.1, 0.9, 1.05, 0.9],
                  opacity: [0.4, 0.7, 0.4, 0.5, 0.4],
                  rotate: [360, 180, 0]
                }}
                transition={{ duration: 10, repeat: Infinity }}
              />

              {/* Central image with floating animation */}
              <motion.div
                className="relative w-full h-full"
                animate={{
                  y: [0, -15, 0, 10, 0],
                  x: [0, 8, 0, -8, 0],
                  rotate: [0, 2, 0, -2, 0]
                }}
                transition={{
                  duration: 12,
                  repeat: Infinity,
                  repeatType: "mirror"
                }}
              >
                <Image
                  src="/hero-bg.svg"
                  alt="About Oyu Intelligence"
                  fill
                  className="object-contain drop-shadow-2xl"
                />
              </motion.div>

              {/* Enhanced floating particles with varied sizes */}
              {Array.from({ length: 12 }).map((_, index) => {
                const colors = ['purple', 'cyan', 'pink', 'blue'];
                const sizes = ['w-1 h-1', 'w-2 h-2', 'w-3 h-3'];
                const color = colors[index % colors.length];
                const size = sizes[index % sizes.length];

                return (
                  <motion.div
                    key={`enhanced-particle-${index}`}
                    className={`absolute ${size} rounded-full ${
                      color === 'purple' ? 'bg-purple-500/60' :
                      color === 'cyan' ? 'bg-cyan-500/60' :
                      color === 'pink' ? 'bg-pink-500/60' : 'bg-blue-500/60'
                    }`}
                    style={{
                      left: `${10 + (index * 8) % 80}%`,
                      top: `${15 + (index * 7) % 70}%`,
                    }}
                    animate={{
                      y: [0, -25, 0, 15, 0],
                      x: [0, 15, 0, -15, 0],
                      opacity: [0.3, 0.8, 0.3, 0.6, 0.3],
                      scale: [0.8, 1.4, 0.8, 1.2, 0.8],
                    }}
                    transition={{
                      duration: 4 + index * 0.5,
                      repeat: Infinity,
                      delay: index * 0.3,
                      ease: "easeInOut"
                    }}
                  />
                );
              })}

              {/* Orbital rings */}
              {[1, 2, 3].map((ring) => (
                <motion.div
                  key={`ring-${ring}`}
                  className={`absolute border border-purple-500/20 rounded-full`}
                  style={{
                    width: `${60 + ring * 40}%`,
                    height: `${60 + ring * 40}%`,
                    top: '50%',
                    left: '50%',
                    transform: 'translate(-50%, -50%)'
                  }}
                  animate={{
                    rotate: ring % 2 === 0 ? [0, 360] : [360, 0],
                    opacity: [0.1, 0.3, 0.1]
                  }}
                  transition={{
                    duration: 20 + ring * 5,
                    repeat: Infinity,
                    ease: "linear"
                  }}
                />
              ))}
            </div>

            {/* Side decorative elements */}
            <motion.div
              className="absolute -top-8 -right-8 w-16 h-16 bg-gradient-to-br from-purple-500/30 to-cyan-500/30 rounded-2xl backdrop-blur-sm border border-purple-500/20"
              animate={{
                rotate: [0, 360],
                scale: [1, 1.1, 1]
              }}
              transition={{
                duration: 8,
                repeat: Infinity
              }}
            />

            <motion.div
              className="absolute -bottom-8 -left-8 w-12 h-12 bg-gradient-to-br from-cyan-500/30 to-purple-500/30 rounded-xl backdrop-blur-sm border border-cyan-500/20"
              animate={{
                rotate: [360, 0],
                scale: [1, 1.2, 1]
              }}
              transition={{
                duration: 6,
                repeat: Infinity
              }}
            />
          </motion.div>
        </div>
      </div>
    </section>
  );
};
