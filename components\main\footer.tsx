"use client";

import { motion, useInView } from "framer-motion";
import Link from "next/link";
import { useRef, useState } from "react";
import { FaEnvelope, FaMapMarkerAlt, FaPhone } from "react-icons/fa";

import { FOOTER_DATA } from "@/constants";
import { GlowingEffect } from "@/components/ui/glowing-effect";
import { staggerContainer, fadeIn, slideInFromLeft, slideInFromRight } from "@/lib/motion";

export const Footer = () => {
  const footerRef = useRef(null);
  const isInView = useInView(footerRef, { once: true, amount: 0.1 });
  const [hoveredColumn, setHoveredColumn] = useState<number | null>(null);

  return (
    <footer
      ref={footerRef}
      className="relative w-full bg-transparent text-gray-200 mt-20 overflow-hidden"
    >
      {/* Background gradient overlay */}
      <div className="absolute inset-0 bg-gradient-to-t from-[rgba(3,0,20,0.95)] via-[rgba(15,5,40,0.8)] to-transparent pointer-events-none" />

      {/* Main footer content */}
      <motion.div
        variants={staggerContainer(0.1, 0.2)}
        initial="hidden"
        animate={isInView ? "visible" : "hidden"}
        className="relative z-10 max-w-7xl mx-auto px-6 py-16"
      >
        {/* Top section with logo and company info */}
        <motion.div
          variants={fadeIn(0, 0.8)}
          className="text-center mb-16"
        >
          <div className="flex flex-col items-center space-y-6">
            {/* Company Logo/Name */}
            <motion.div
              whileHover={{ scale: 1.05 }}
              transition={{ type: "spring", stiffness: 300, damping: 20 }}
              className="relative"
            >
              <h2 className="text-4xl md:text-5xl font-bold bg-gradient-to-r from-purple-400 via-cyan-400 to-purple-600 bg-clip-text text-transparent">
                Oyu Intelligence
              </h2>
              <div className="absolute -inset-1 bg-gradient-to-r from-purple-600 to-cyan-600 rounded-lg blur opacity-20 group-hover:opacity-40 transition duration-1000"></div>
            </motion.div>

            {/* Company tagline */}
            <motion.p
              variants={fadeIn(0.2, 0.8)}
              className="text-lg text-gray-300 max-w-2xl leading-relaxed"
            >
              Transforming businesses through innovative AI solutions, cutting-edge mobile applications,
              and premium digital experiences.
            </motion.p>
          </div>
        </motion.div>

        {/* Footer columns */}
        <motion.div
          variants={staggerContainer(0.1, 0.4)}
          className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 mb-12"
        >
          {FOOTER_DATA.map((column, columnIndex) => (
            <motion.div
              key={column.title}
              variants={fadeIn(columnIndex * 0.1, 0.6)}
              onMouseEnter={() => setHoveredColumn(columnIndex)}
              onMouseLeave={() => setHoveredColumn(null)}
              className="relative group"
            >
              {/* Column container with glassmorphism effect */}
              <div className="relative p-6 rounded-2xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.4)] via-[rgba(15,5,40,0.5)] to-[rgba(3,0,20,0.6)] border border-[#7042F88B] hover:border-purple-500/50 transition-all duration-500">
                <GlowingEffect
                  spread={80}
                  glow={hoveredColumn === columnIndex}
                  disabled={hoveredColumn !== columnIndex}
                  proximity={120}
                  inactiveZone={0.1}
                  blur={3}
                  borderWidth={2}
                />

                {/* Column title */}
                <motion.h3
                  whileHover={{ scale: 1.05 }}
                  className="font-bold text-xl text-white mb-6 relative z-10"
                >
                  {column.title}
                </motion.h3>

                {/* Column links */}
                <div className="space-y-3 relative z-10">
                  {column.data.map(({ icon: Icon, name, link }, linkIndex) => (
                    <motion.div
                      key={`${column.title}-${name}`}
                      initial={{ opacity: 0, x: -20 }}
                      animate={isInView ? { opacity: 1, x: 0 } : { opacity: 0, x: -20 }}
                      transition={{ delay: (columnIndex * 0.1) + (linkIndex * 0.05), duration: 0.5 }}
                    >
                      <Link
                        href={link}
                        target={link.startsWith('http') ? "_blank" : "_self"}
                        rel={link.startsWith('http') ? "noreferrer noopener" : undefined}
                        className="group/link flex items-center space-x-3 text-gray-300 hover:text-white transition-all duration-300 p-2 rounded-lg hover:bg-white/5"
                      >
                        {Icon && (
                          <motion.div
                            whileHover={{ scale: 1.2, rotate: 5 }}
                            transition={{ type: "spring", stiffness: 300, damping: 20 }}
                          >
                            <Icon className="w-5 h-5 text-purple-400 group-hover/link:text-cyan-400 transition-colors duration-300" />
                          </motion.div>
                        )}
                        <span className="text-sm group-hover/link:translate-x-1 transition-transform duration-300">
                          {name}
                        </span>
                      </Link>
                    </motion.div>
                  ))}
                </div>
              </div>
            </motion.div>
          ))}
        </motion.div>

        {/* Contact information section */}
        <motion.div
          variants={fadeIn(0.6, 0.8)}
          className="mb-12"
        >
          <div className="relative p-8 rounded-3xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.3)] via-[rgba(15,5,40,0.4)] to-[rgba(3,0,20,0.5)] border border-[#7042F88B]">
            <GlowingEffect
              spread={120}
              glow={true}
              disabled={false}
              proximity={150}
              inactiveZone={0.05}
              blur={4}
              borderWidth={3}
            />

            <div className="relative z-10">
              <motion.h3
                variants={fadeIn(0.8, 0.6)}
                className="text-2xl font-bold text-white mb-8 text-center"
              >
                Get In Touch
              </motion.h3>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                {/* Email */}
                <motion.div
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  className="flex flex-col items-center text-center group"
                >
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-cyan-500 flex items-center justify-center mb-4 group-hover:shadow-lg group-hover:shadow-purple-500/30 transition-all duration-300">
                    <FaEnvelope className="w-7 h-7 text-white" />
                  </div>
                  <h4 className="text-white font-semibold mb-2">Email Us</h4>
                  <Link
                    href="mailto:<EMAIL>"
                    className="text-gray-300 hover:text-cyan-400 transition-colors duration-300 text-sm"
                  >
                    <EMAIL>
                  </Link>
                </motion.div>

                {/* Phone */}
                <motion.div
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  className="flex flex-col items-center text-center group"
                >
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-cyan-500 to-purple-500 flex items-center justify-center mb-4 group-hover:shadow-lg group-hover:shadow-cyan-500/30 transition-all duration-300">
                    <FaPhone className="w-7 h-7 text-white" />
                  </div>
                  <h4 className="text-white font-semibold mb-2">Call Us</h4>
                  <Link
                    href="tel:86970213"
                    className="text-gray-300 hover:text-cyan-400 transition-colors duration-300 text-sm"
                  >
                    +976 86970213
                  </Link>
                </motion.div>

                {/* Location */}
                <motion.div
                  whileHover={{ scale: 1.05, y: -5 }}
                  transition={{ type: "spring", stiffness: 300, damping: 20 }}
                  className="flex flex-col items-center text-center group"
                >
                  <div className="w-16 h-16 rounded-full bg-gradient-to-br from-purple-500 to-pink-500 flex items-center justify-center mb-4 group-hover:shadow-lg group-hover:shadow-purple-500/30 transition-all duration-300">
                    <FaMapMarkerAlt className="w-7 h-7 text-white" />
                  </div>
                  <h4 className="text-white font-semibold mb-2">Visit Us</h4>
                  <Link
                    href="https://maps.app.goo.gl/yj1MUxqiMvf6hha69"
                    target="_blank"
                    rel="noreferrer noopener"
                    className="text-gray-300 hover:text-cyan-400 transition-colors duration-300 text-sm text-center"
                  >
                    Sambuu St 47, CHD - 5 khoroo<br />
                    Ulaanbaatar 15171, Mongolia
                  </Link>
                </motion.div>
              </div>
            </div>
          </div>
        </motion.div>

        {/* Newsletter subscription section */}
        <motion.div
          variants={fadeIn(0.8, 0.8)}
          className="mb-12"
        >
          <div className="relative p-8 rounded-3xl backdrop-blur-xl bg-gradient-to-br from-[rgba(30,10,60,0.3)] via-[rgba(15,5,40,0.4)] to-[rgba(3,0,20,0.5)] border border-[#7042F88B] text-center">
            <motion.h3
              variants={fadeIn(1.0, 0.6)}
              className="text-2xl font-bold text-white mb-4"
            >
              Stay Updated
            </motion.h3>
            <motion.p
              variants={fadeIn(1.2, 0.6)}
              className="text-gray-300 mb-6 max-w-md mx-auto"
            >
              Subscribe to our newsletter for the latest updates on AI innovations and digital solutions.
            </motion.p>

            <motion.div
              variants={fadeIn(1.4, 0.6)}
              className="flex flex-col sm:flex-row gap-4 max-w-md mx-auto"
            >
              <input
                type="email"
                placeholder="Enter your email"
                className="flex-1 px-4 py-3 rounded-lg bg-white/10 border border-white/20 text-white placeholder-gray-400 focus:outline-none focus:border-purple-500 focus:bg-white/15 transition-all duration-300"
              />
              <motion.button
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
                className="px-6 py-3 bg-gradient-to-r from-purple-600 to-cyan-600 text-white font-semibold rounded-lg hover:from-purple-700 hover:to-cyan-700 transition-all duration-300 shadow-lg hover:shadow-purple-500/25"
              >
                Subscribe
              </motion.button>
            </motion.div>
          </div>
        </motion.div>

        {/* Bottom section with copyright and additional links */}
        <motion.div
          variants={fadeIn(1.0, 0.8)}
          className="border-t border-white/10 pt-8"
        >
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            {/* Copyright */}
            <motion.div
              variants={slideInFromLeft(1.2)}
              className="text-center md:text-left"
            >
              <p className="text-gray-400 text-sm">
                &copy; {new Date().getFullYear()} Oyu Intelligence LLC. All rights reserved.
              </p>
              <p className="text-gray-500 text-xs mt-1">
                Crafted with ❤️ in Mongolia
              </p>
            </motion.div>

            {/* Additional links */}
            <motion.div
              variants={slideInFromRight(1.2)}
              className="flex space-x-6 text-sm"
            >
              <Link
                href="/privacy"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Privacy Policy
              </Link>
              <Link
                href="/terms"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Terms of Service
              </Link>
              <Link
                href="/sitemap"
                className="text-gray-400 hover:text-white transition-colors duration-300"
              >
                Sitemap
              </Link>
            </motion.div>
          </div>
        </motion.div>
      </motion.div>

      {/* Decorative elements */}
      <div className="absolute top-0 left-1/4 w-64 h-64 bg-purple-500/10 rounded-full blur-3xl"></div>
      <div className="absolute bottom-0 right-1/4 w-64 h-64 bg-cyan-500/10 rounded-full blur-3xl"></div>
    </footer>
  );
};
